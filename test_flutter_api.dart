import 'package:flutter/material.dart';
import 'lib/services/api_service.dart';

void main() {
  runApp(ApiTestApp());
}

class ApiTestApp extends StatelessWidget {
  const ApiTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(title: 'API Test', home: ApiTestScreen());
  }
}

class ApiTestScreen extends StatefulWidget {
  const ApiTestScreen({super.key});

  @override
  State<ApiTestScreen> createState() => _ApiTestScreenState();
}

class _ApiTestScreenState extends State<ApiTestScreen> {
  String _result = 'اضغط على الأزرار لاختبار API';
  bool _loading = false;

  Future<void> _testLogin() async {
    setState(() {
      _loading = true;
      _result = 'جاري اختبار تسجيل الدخول...';
    });

    try {
      final result = await ApiService.login('<EMAIL>', 'admin123');

      setState(() {
        _result = 'نتيجة تسجيل الدخول:\n${result.toString()}';
        _loading = false;
      });
    } catch (e) {
      setState(() {
        _result = 'خطأ في تسجيل الدخول:\n$e';
        _loading = false;
      });
    }
  }

  Future<void> _testNotifications() async {
    setState(() {
      _loading = true;
      _result = 'جاري جلب الإشعارات...';
    });

    try {
      final result = await ApiService.getNotifications();

      setState(() {
        _result = 'نتيجة الإشعارات:\n${result.toString()}';
        _loading = false;
      });
    } catch (e) {
      setState(() {
        _result = 'خطأ في جلب الإشعارات:\n$e';
        _loading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('اختبار API'), backgroundColor: Colors.blue),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'اختبار الاتصال بـ Mock API',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 20),

            ElevatedButton(
              onPressed: _loading ? null : _testLogin,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                padding: EdgeInsets.symmetric(vertical: 12),
              ),
              child: Text('اختبار تسجيل الدخول'),
            ),

            SizedBox(height: 10),

            ElevatedButton(
              onPressed: _loading ? null : _testNotifications,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                padding: EdgeInsets.symmetric(vertical: 12),
              ),
              child: Text('اختبار جلب الإشعارات'),
            ),

            SizedBox(height: 20),

            if (_loading) Center(child: CircularProgressIndicator()),

            Expanded(
              child: Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _result,
                    style: TextStyle(fontFamily: 'monospace'),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
