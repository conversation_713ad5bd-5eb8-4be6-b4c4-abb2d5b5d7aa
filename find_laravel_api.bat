@echo off
echo ========================================
echo      البحث عن Laravel API الصحيح
echo ========================================
echo.

echo 🔍 اختبار العناوين المحتملة...
echo.

echo 1. اختبار localhost:8000 (php artisan serve)...
curl -s -o nul -w "Status: %%{http_code}\n" http://localhost:8000/api/test 2>nul
if %errorlevel% equ 0 (
    echo ✅ localhost:8000 متاح
    curl -s http://localhost:8000/api/test 2>nul
) else (
    echo ❌ localhost:8000 غير متاح
)
echo.

echo 2. اختبار 127.0.0.1:8000...
curl -s -o nul -w "Status: %%{http_code}\n" http://127.0.0.1:8000/api/test 2>nul
if %errorlevel% equ 0 (
    echo ✅ 127.0.0.1:8000 متاح
    curl -s http://127.0.0.1:8000/api/test 2>nul
) else (
    echo ❌ 127.0.0.1:8000 غير متاح
)
echo.

echo 3. اختبار notysend.test...
curl -s -o nul -w "Status: %%{http_code}\n" http://notysend.test/api/test 2>nul
if %errorlevel% equ 0 (
    echo ✅ notysend.test متاح
    curl -s http://notysend.test/api/test 2>nul
) else (
    echo ❌ notysend.test غير متاح
)
echo.

echo 4. اختبار localhost/notysend/public (XAMPP/WAMP)...
curl -s -o nul -w "Status: %%{http_code}\n" http://localhost/notysend/public/api/test 2>nul
if %errorlevel% equ 0 (
    echo ✅ localhost/notysend/public متاح
    curl -s http://localhost/notysend/public/api/test 2>nul
) else (
    echo ❌ localhost/notysend/public غير متاح
)
echo.

echo 5. اختبار localhost:80/notysend/public...
curl -s -o nul -w "Status: %%{http_code}\n" http://localhost:80/notysend/public/api/test 2>nul
if %errorlevel% equ 0 (
    echo ✅ localhost:80/notysend/public متاح
    curl -s http://localhost:80/notysend/public/api/test 2>nul
) else (
    echo ❌ localhost:80/notysend/public غير متاح
)
echo.

echo ========================================
echo           نتائج البحث
echo ========================================
echo.
echo إذا لم يعمل أي من العناوين أعلاه، فقد تحتاج إلى:
echo 1. تشغيل Laravel بـ: php artisan serve
echo 2. تشغيل Laragon أو XAMPP
echo 3. التحقق من مجلد Laravel
echo.
pause
