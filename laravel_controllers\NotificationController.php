<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class NotificationController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();
        
        $notifications = Notification::where(function ($query) use ($user) {
            $query->where('recipient_type', 'all')
                  ->orWhere('recipient_type', $user->user_type . 's')
                  ->orWhere(function ($q) use ($user) {
                      $q->where('recipient_type', 'specific')
                        ->whereJsonContains('specific_recipients', $user->id);
                  });
        })
        ->with('sender:id,name')
        ->orderBy('created_at', 'desc')
        ->get()
        ->map(function ($notification) {
            return [
                'id' => $notification->id,
                'title' => $notification->title,
                'message' => $notification->message,
                'type' => $notification->type,
                'recipient_type' => $notification->recipient_type,
                'sender_id' => $notification->sender_id,
                'sender_name' => $notification->sender->name ?? 'Unknown',
                'is_read' => $notification->is_read,
                'has_attachment' => !empty($notification->attachment_url),
                'attachment_name' => $notification->attachment_name,
                'attachment_url' => $notification->attachment_url,
                'created_at' => $notification->created_at,
                'updated_at' => $notification->updated_at,
            ];
        });

        return response()->json([
            'success' => true,
            'notifications' => $notifications
        ]);
    }

    public function sent(Request $request)
    {
        $user = $request->user();
        
        if ($user->user_type !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $notifications = Notification::where('sender_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'title' => $notification->title,
                    'message' => $notification->message,
                    'type' => $notification->type,
                    'recipient_type' => $notification->recipient_type,
                    'sender_id' => $notification->sender_id,
                    'sender_name' => $notification->sender->name ?? 'Unknown',
                    'is_read' => $notification->is_read,
                    'has_attachment' => !empty($notification->attachment_url),
                    'attachment_name' => $notification->attachment_name,
                    'attachment_url' => $notification->attachment_url,
                    'created_at' => $notification->created_at,
                    'updated_at' => $notification->updated_at,
                ];
            });

        return response()->json([
            'success' => true,
            'notifications' => $notifications
        ]);
    }

    public function store(Request $request)
    {
        $user = $request->user();
        
        if ($user->user_type !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'type' => 'required|in:general,urgent,announcement,reminder',
            'recipient_type' => 'required|in:all,students,teachers,specific',
            'specific_recipients' => 'required_if:recipient_type,specific|array',
            'specific_recipients.*' => 'exists:users,id',
            'attachment_url' => 'nullable|url',
            'attachment_name' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $notification = Notification::create([
            'title' => $request->title,
            'message' => $request->message,
            'type' => $request->type,
            'recipient_type' => $request->recipient_type,
            'sender_id' => $user->id,
            'specific_recipients' => $request->specific_recipients,
            'attachment_url' => $request->attachment_url,
            'attachment_name' => $request->attachment_name,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Notification sent successfully',
            'notification' => [
                'id' => $notification->id,
                'title' => $notification->title,
                'message' => $notification->message,
                'type' => $notification->type,
                'recipient_type' => $notification->recipient_type,
                'sender_id' => $notification->sender_id,
                'sender_name' => $user->name,
                'is_read' => $notification->is_read,
                'has_attachment' => !empty($notification->attachment_url),
                'attachment_name' => $notification->attachment_name,
                'attachment_url' => $notification->attachment_url,
                'created_at' => $notification->created_at,
                'updated_at' => $notification->updated_at,
            ]
        ], 201);
    }

    public function markAsRead(Request $request, $id)
    {
        $user = $request->user();
        
        $notification = Notification::where('id', $id)
            ->where(function ($query) use ($user) {
                $query->where('recipient_type', 'all')
                      ->orWhere('recipient_type', $user->user_type . 's')
                      ->orWhere(function ($q) use ($user) {
                          $q->where('recipient_type', 'specific')
                            ->whereJsonContains('specific_recipients', $user->id);
                      });
            })
            ->first();

        if (!$notification) {
            return response()->json([
                'success' => false,
                'message' => 'Notification not found'
            ], 404);
        }

        $notification->update(['is_read' => true]);

        return response()->json([
            'success' => true,
            'message' => 'Notification marked as read'
        ]);
    }

    public function markAllAsRead(Request $request)
    {
        $user = $request->user();
        
        Notification::where(function ($query) use ($user) {
            $query->where('recipient_type', 'all')
                  ->orWhere('recipient_type', $user->user_type . 's')
                  ->orWhere(function ($q) use ($user) {
                      $q->where('recipient_type', 'specific')
                        ->whereJsonContains('specific_recipients', $user->id);
                  });
        })->update(['is_read' => true]);

        return response()->json([
            'success' => true,
            'message' => 'All notifications marked as read'
        ]);
    }

    public function destroy(Request $request, $id)
    {
        $user = $request->user();
        
        if ($user->user_type !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $notification = Notification::find($id);

        if (!$notification) {
            return response()->json([
                'success' => false,
                'message' => 'Notification not found'
            ], 404);
        }

        $notification->delete();

        return response()->json([
            'success' => true,
            'message' => 'Notification deleted successfully'
        ]);
    }

    public function stats(Request $request)
    {
        $user = $request->user();
        
        if ($user->user_type !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $totalNotifications = Notification::count();
        $unreadNotifications = Notification::where('is_read', false)->count();
        $readNotifications = $totalNotifications - $unreadNotifications;

        return response()->json([
            'success' => true,
            'stats' => [
                'total_notifications' => $totalNotifications,
                'unread_notifications' => $unreadNotifications,
                'read_notifications' => $readNotifications,
            ]
        ]);
    }
}
