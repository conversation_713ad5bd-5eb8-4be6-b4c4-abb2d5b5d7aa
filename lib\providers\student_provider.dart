import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../models/student_model.dart';
import '../services/api_service.dart';

class StudentProvider with ChangeNotifier {
  List<Student> _students = [];
  bool _isLoading = false;
  String? _error;

  List<Student> get students => _students;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Add a new student
  Future<bool> addStudent(Student student) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await http.post(
        Uri.parse('${ApiService.baseUrl}/students'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode({
          'full_name': student.fullName,
          'nationality': student.nationality,
          'phone_number': student.phoneNumber,
          'class': student.studentClass,
          'parent_phone_number': student.parentPhoneNumber,
          'password': student.password ?? 'student123',
        }),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData['success'] == true) {
          // Add to local list
          _students.add(student);
          _isLoading = false;
          notifyListeners();
          return true;
        } else {
          _error = responseData['message'] ?? 'فشل في إضافة الطالب';
        }
      } else {
        _error = 'خطأ في الخادم: ${response.statusCode}';
      }
    } catch (e) {
      _error = 'خطأ في الاتصال: $e';
    }

    _isLoading = false;
    notifyListeners();
    return false;
  }

  // Bulk import students
  Future<Map<String, dynamic>> bulkImportStudents(List<Student> students) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final studentsData = students.map((student) => {
        'full_name': student.fullName,
        'nationality': student.nationality,
        'phone_number': student.phoneNumber,
        'class': student.studentClass,
        'parent_phone_number': student.parentPhoneNumber,
        'password': student.password ?? 'student123',
      }).toList();

      final response = await http.post(
        Uri.parse('${ApiService.baseUrl}/students/bulk-import'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode({
          'students': studentsData,
        }),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData['success'] == true) {
          // Refresh students list
          await refreshStudents();
          _isLoading = false;
          notifyListeners();
          return {
            'success': true,
            'imported_count': responseData['imported_count'] ?? 0,
            'error_count': responseData['error_count'] ?? 0,
            'errors': responseData['errors'] ?? [],
            'message': responseData['message'] ?? 'تم الاستيراد بنجاح',
          };
        } else {
          _error = responseData['message'] ?? 'فشل في استيراد الطلاب';
        }
      } else {
        _error = 'خطأ في الخادم: ${response.statusCode}';
      }
    } catch (e) {
      _error = 'خطأ في الاتصال: $e';
    }

    _isLoading = false;
    notifyListeners();
    return {
      'success': false,
      'message': _error ?? 'فشل في الاستيراد',
    };
  }

  // Get all students
  Future<void> refreshStudents() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await http.get(
        Uri.parse('${ApiService.baseUrl}/students'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData['success'] == true) {
          final studentsData = responseData['students'] as List;
          _students = studentsData.map((data) => Student.fromMap(data)).toList();
        } else {
          _error = responseData['message'] ?? 'فشل في تحميل قائمة الطلاب';
        }
      } else {
        _error = 'خطأ في الخادم: ${response.statusCode}';
      }
    } catch (e) {
      _error = 'خطأ في الاتصال: $e';
    }

    _isLoading = false;
    notifyListeners();
  }

  // Delete student
  Future<bool> deleteStudent(int studentId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await http.delete(
        Uri.parse('${ApiService.baseUrl}/students/$studentId'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData['success'] == true) {
          // Remove from local list
          _students.removeWhere((student) => student.id == studentId);
          _isLoading = false;
          notifyListeners();
          return true;
        } else {
          _error = responseData['message'] ?? 'فشل في حذف الطالب';
        }
      } else {
        _error = 'خطأ في الخادم: ${response.statusCode}';
      }
    } catch (e) {
      _error = 'خطأ في الاتصال: $e';
    }

    _isLoading = false;
    notifyListeners();
    return false;
  }

  // Search students
  List<Student> searchStudents(String query) {
    if (query.isEmpty) return _students;
    
    return _students.where((student) {
      return student.fullName.toLowerCase().contains(query.toLowerCase()) ||
             student.phoneNumber.contains(query) ||
             student.studentClass.toLowerCase().contains(query.toLowerCase()) ||
             student.nationality.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }
}
