@echo off
echo ========================================
echo        اختبار سريع للاتصال بـ API
echo ========================================
echo.

echo 1. اختبار Health Check...
curl -s http://localhost:8000/api/health
echo.
echo.

echo 2. اختبار Test Route...
curl -s http://localhost:8000/api/test
echo.
echo.

echo 3. اختبار Login Endpoint...
curl -X POST http://localhost:8000/api/auth/login ^
  -H "Content-Type: application/json" ^
  -H "Accept: application/json" ^
  -d "{\"email\":\"<EMAIL>\",\"password\":\"admin123\"}"
echo.
echo.

echo 4. اختبار عناوين بديلة...
echo.

echo اختبار 127.0.0.1:8000...
curl -s http://127.0.0.1:8000/api/test
echo.
echo.

echo اختبار notysend.test...
curl -s http://notysend.test/api/test
echo.
echo.

echo ========================================
echo           انتهى الاختبار
echo ========================================
pause
