# دليل استيراد الطلاب من Excel

## 🎯 **الميزة الجديدة: استيراد الطلاب بالجملة من Excel**

تم إضافة ميزة جديدة تسمح للمدير بإستيراد عدة طلاب دفعة واحدة من ملف Excel بدلاً من إدخالهم واحداً تلو الآخر.

## 🚀 **كيفية الوصول للميزة:**

### 1. **من لوحة تحكم المدير:**
1. سجل دخول كمدير
2. في الصفحة الرئيسية، ابحث عن "استيراد الطلاب من Excel"
3. اضغط على الكارت

### 2. **من قائمة إدارة الطلاب:**
- يمكن إضافة زر في شاشة قائمة الطلاب لاحقاً

## 📋 **خطوات الاستيراد:**

### الخطوة 1: تحضير ملف Excel
1. **تحميل القالب**: اضغط على أيقونة التحميل في أعلى الشاشة
2. **إنشاء ملف Excel** جديد بالأعمدة التالية:
   - **العمود A**: الاسم الكامل
   - **العمود B**: الجنسية  
   - **العمود C**: رقم الهاتف
   - **العمود D**: الصف
   - **العمود E**: رقم هاتف ولي الأمر

### الخطوة 2: ملء البيانات
```
| الاسم الكامل      | الجنسية | رقم الهاتف  | الصف        | رقم هاتف ولي الأمر |
|------------------|---------|-------------|-------------|-------------------|
| أحمد محمد علي     | سعودي   | **********  | الصف الأول  | **********        |
| فاطمة أحمد محمد   | سعودي   | **********  | الصف الثاني | 0507654322        |
| محمد علي أحمد     | مصري    | 0501234569  | الصف الثالث | 0507654323        |
```

### الخطوة 3: رفع الملف
1. اضغط على "اختيار ملف Excel"
2. اختر الملف من جهازك (.xlsx أو .xls)
3. انتظر معالجة الملف

### الخطوة 4: مراجعة البيانات
1. ستظهر معاينة للطلاب المستخرجين من الملف
2. راجع البيانات للتأكد من صحتها
3. يمكنك مسح البيانات والبدء من جديد إذا لزم الأمر

### الخطوة 5: الحفظ
1. اضغط على "حفظ جميع الطلاب"
2. انتظر اكتمال عملية الاستيراد
3. ستظهر رسالة تأكيد بعدد الطلاب المستوردين

## ⚠️ **متطلبات مهمة:**

### 1. **الحقول المطلوبة:**
- جميع الحقول الخمسة **مطلوبة**
- لا يمكن ترك أي حقل فارغ

### 2. **رقم الهاتف:**
- يجب أن يكون **فريد** (غير مكرر)
- إذا كان رقم موجود مسبقاً، سيتم تخطي هذا الطالب

### 3. **تنسيق الملف:**
- يجب أن يكون الملف بصيغة Excel (.xlsx أو .xls)
- الصف الأول يجب أن يحتوي على العناوين
- البيانات تبدأ من الصف الثاني

## 🔧 **الميزات التقنية:**

### 1. **المعالجة الذكية:**
- تخطي الصفوف الفارغة تلقائياً
- التحقق من صحة البيانات قبل الحفظ
- عرض أخطاء مفصلة للصفوف المرفوضة

### 2. **التقارير:**
- عدد الطلاب المستوردين بنجاح
- عدد الطلاب المرفوضين
- تفاصيل الأخطاء لكل صف مرفوض

### 3. **الأمان:**
- التحقق من صحة البيانات
- منع الأرقام المكررة
- كلمة مرور افتراضية: "student123"

## 📱 **الملفات المضافة:**

### 1. **شاشة الاستيراد:**
- `lib/screens/admin/import_students_screen.dart`

### 2. **تحديث Student Provider:**
- `lib/providers/student_provider.dart`
- إضافة دالة `bulkImportStudents()`

### 3. **تحديث Mock API:**
- `mock_api_server.js`
- إضافة endpoint: `POST /api/students/bulk-import`

### 4. **Dependencies الجديدة:**
```yaml
file_picker: ^8.0.0+1  # لاختيار الملفات
excel: ^4.0.6          # لقراءة ملفات Excel
path: ^1.9.0           # للتعامل مع مسارات الملفات
```

## 🎨 **واجهة المستخدم:**

### 1. **التصميم:**
- واجهة عربية بالكامل
- ألوان متناسقة مع التطبيق
- أيقونات واضحة ومعبرة

### 2. **تجربة المستخدم:**
- تعليمات واضحة خطوة بخطوة
- رسائل خطأ مفهومة
- مؤشرات تحميل أثناء المعالجة

## 🧪 **الاختبار:**

### 1. **بيانات اختبار:**
```csv
أحمد محمد علي,سعودي,**********,الصف الأول,**********
فاطمة أحمد محمد,سعودي,**********,الصف الثاني,0507654322
محمد علي أحمد,مصري,0501234569,الصف الثالث,0507654323
```

### 2. **سيناريوهات الاختبار:**
- ✅ استيراد ملف صحيح
- ✅ ملف بصفوف فارغة
- ✅ ملف بأرقام مكررة
- ✅ ملف ببيانات ناقصة
- ✅ ملف بتنسيق خاطئ

## 🔄 **التطوير المستقبلي:**

### 1. **ميزات إضافية:**
- تصدير قائمة الطلاب إلى Excel
- قوالب متعددة للاستيراد
- استيراد صور الطلاب
- تحديث بيانات الطلاب الموجودين

### 2. **تحسينات:**
- دعم ملفات CSV
- معاينة أكثر تفصيلاً
- إمكانية تعديل البيانات قبل الحفظ

---

**🎉 الميزة جاهزة للاستخدام! جرب استيراد الطلاب الآن.**
