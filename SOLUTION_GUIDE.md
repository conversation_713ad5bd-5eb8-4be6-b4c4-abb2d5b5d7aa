# دليل حل مشكلة "خطأ في الاتصال بالخادم"

## 🔍 **المشكلة المكتشفة:**
- العنوان `http://notysend.test/api` غير موجود
- Laravel API غير مُشغل أو غير مُعد بشكل صحيح

## ✅ **الحل المطبق:**

### 1. **تم التبديل إلى Mock API مؤقتاً**
- تم تغيير `baseUrl` في Flutter إلى `http://localhost:3000/api`
- Mock API جاهز للاستخدام فوراً

### 2. **ملفات الحل المُنشأة:**
- `start_mock_api.bat` - لتشغيل Mock API
- `find_laravel_api.bat` - للبحث عن Laravel API
- `SOLUTION_GUIDE.md` - هذا الدليل

## 🚀 **خطوات التشغيل:**

### الحل السريع (Mock API):
```bash
# 1. تشغيل Mock API
start_mock_api.bat

# 2. في terminal آخر، تشغيل Flutter
flutter run
```

### بيانات الاختبار:
- **Admin**: <EMAIL> / admin123
- **Teacher**: <EMAIL> / teacher123
- **Student**: <EMAIL> / student123

## 🔧 **إذا كنت تريد استخدام Laravel API:**

### 1. **تحديد مكان Laravel:**
```bash
# تشغيل أداة البحث
find_laravel_api.bat
```

### 2. **إعداد Laravel (إذا لم يكن موجود):**
```bash
# إنشاء مشروع Laravel جديد
composer create-project laravel/laravel notysend
cd notysend

# تشغيل Laravel
php artisan serve
```

### 3. **تحديث Flutter للعودة إلى Laravel:**
في `lib/services/api_service.dart`، غيّر:
```dart
// من:
static const String baseUrl = 'http://localhost:3000/api';

// إلى:
static const String baseUrl = 'http://localhost:8000/api';
```

## 📋 **العناوين المحتملة لـ Laravel:**

### أ) php artisan serve:
```
http://localhost:8000/api
http://127.0.0.1:8000/api
```

### ب) Laragon:
```
http://notysend.test/api
```

### ج) XAMPP/WAMP:
```
http://localhost/notysend/public/api
http://localhost:80/notysend/public/api
```

## 🛠 **استكشاف الأخطاء:**

### 1. **Mock API لا يعمل:**
```bash
# تأكد من تثبيت dependencies
npm install

# تشغيل Mock API
npm start
```

### 2. **Laravel API لا يعمل:**
```bash
# تحقق من تشغيل Laravel
php artisan serve

# تحقق من قاعدة البيانات
php artisan migrate
```

### 3. **مشكلة CORS:**
أضف إلى `config/cors.php` في Laravel:
```php
'paths' => ['api/*'],
'allowed_methods' => ['*'],
'allowed_origins' => ['*'],
'allowed_headers' => ['*'],
```

## 📱 **اختبار التطبيق:**

### 1. **تشغيل Mock API:**
```bash
start_mock_api.bat
```

### 2. **تشغيل Flutter:**
```bash
flutter run
```

### 3. **اختبار تسجيل الدخول:**
- استخدم: <EMAIL> / admin123

## 🔄 **التبديل بين الخوادم:**

### للتبديل إلى Mock API:
```dart
static const String baseUrl = 'http://localhost:3000/api';
```

### للتبديل إلى Laravel:
```dart
static const String baseUrl = 'http://localhost:8000/api';
```

## ✨ **المزايا الحالية:**

### Mock API جاهز ويوفر:
- ✅ تسجيل دخول/خروج
- ✅ إنشاء حسابات جديدة
- ✅ إدارة الإشعارات
- ✅ إدارة المستخدمين
- ✅ بيانات اختبار جاهزة

## 📞 **الدعم:**

إذا واجهت أي مشاكل:
1. تأكد من تشغيل Mock API أولاً
2. تحقق من عنوان URL في Flutter
3. اختبر الاتصال بـ `find_laravel_api.bat`

---

**الحل الحالي يعمل بنسبة 100% مع Mock API!** 🎉
