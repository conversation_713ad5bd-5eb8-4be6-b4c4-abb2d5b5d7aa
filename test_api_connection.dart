import 'dart:convert';
import 'package:http/http.dart' as http;

void main() async {
  print('🔍 اختبار الاتصال بـ Laravel API...\n');
  
  // اختبار عناوين مختلفة
  final List<String> testUrls = [
    'http://localhost:8000',
    'http://127.0.0.1:8000',
    'http://notysend.test',
    'http://localhost/notysend/public',
  ];
  
  for (String baseUrl in testUrls) {
    print('📡 اختبار: $baseUrl');
    await testConnection(baseUrl);
    print('');
  }
}

Future<void> testConnection(String baseUrl) async {
  try {
    // اختبار 1: Health Check
    print('  ✅ اختبار Health Check...');
    final healthResponse = await http.get(
      Uri.parse('$baseUrl/api/health'),
      headers: {'Accept': 'application/json'},
    ).timeout(Duration(seconds: 5));
    
    if (healthResponse.statusCode == 200) {
      print('  ✅ Health Check نجح: ${healthResponse.statusCode}');
      final data = json.decode(healthResponse.body);
      print('  📊 البيانات: $data');
    } else {
      print('  ❌ Health Check فشل: ${healthResponse.statusCode}');
    }
    
    // اختبار 2: Test Route
    print('  ✅ اختبار Test Route...');
    final testResponse = await http.get(
      Uri.parse('$baseUrl/api/test'),
      headers: {'Accept': 'application/json'},
    ).timeout(Duration(seconds: 5));
    
    if (testResponse.statusCode == 200) {
      print('  ✅ Test Route نجح: ${testResponse.statusCode}');
      final data = json.decode(testResponse.body);
      print('  📊 البيانات: $data');
    } else {
      print('  ❌ Test Route فشل: ${testResponse.statusCode}');
    }
    
    // اختبار 3: Login Endpoint
    print('  ✅ اختبار Login Endpoint...');
    final loginResponse = await http.post(
      Uri.parse('$baseUrl/api/auth/login'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: json.encode({
        'email': '<EMAIL>',
        'password': 'admin123',
      }),
    ).timeout(Duration(seconds: 5));
    
    print('  📡 Login Response: ${loginResponse.statusCode}');
    if (loginResponse.statusCode == 200 || loginResponse.statusCode == 422) {
      final data = json.decode(loginResponse.body);
      print('  📊 البيانات: $data');
    } else {
      print('  ❌ Login فشل: ${loginResponse.body}');
    }
    
  } catch (e) {
    print('  ❌ خطأ في الاتصال: $e');
  }
}
