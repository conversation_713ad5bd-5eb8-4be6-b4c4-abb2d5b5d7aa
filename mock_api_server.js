const express = require('express');
const cors = require('cors');
const app = express();
const port = 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Mock data
const users = [
  {
    id: 1,
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'admin123', // In real app, this would be hashed
    user_type: 'admin',
    phone: '+1234567890',
    department: 'Administration',
    profile_image: null,
    created_at: '2024-01-01T00:00:00.000Z',
    updated_at: '2024-01-01T00:00:00.000Z',
    is_active: true
  },
  {
    id: 2,
    name: 'Teacher User',
    email: '<EMAIL>',
    password: 'teacher123',
    user_type: 'teacher',
    phone: '+1234567891',
    department: 'Computer Science',
    profile_image: null,
    created_at: '2024-01-01T00:00:00.000Z',
    updated_at: '2024-01-01T00:00:00.000Z',
    is_active: true
  },
  {
    id: 3,
    name: 'Student User',
    email: '<EMAIL>',
    password: 'student123',
    user_type: 'student',
    phone: '+1234567892',
    department: 'Computer Science',
    profile_image: null,
    created_at: '2024-01-01T00:00:00.000Z',
    updated_at: '2024-01-01T00:00:00.000Z',
    is_active: true
  }
];

const notifications = [
  {
    id: 1,
    title: 'Welcome to NotiSend',
    message: 'This is a welcome notification for new users.',
    type: 'general',
    recipient_type: 'all',
    sender_id: 1,
    sender_name: 'Admin User',
    is_read: false,
    has_attachment: false,
    attachment_name: null,
    attachment_url: null,
    created_at: '2024-01-01T00:00:00.000Z',
    updated_at: '2024-01-01T00:00:00.000Z'
  },
  {
    id: 2,
    title: 'System Maintenance',
    message: 'The system will be under maintenance tomorrow from 2 AM to 4 AM.',
    type: 'urgent',
    recipient_type: 'all',
    sender_id: 1,
    sender_name: 'Admin User',
    is_read: false,
    has_attachment: false,
    attachment_name: null,
    attachment_url: null,
    created_at: '2024-01-02T00:00:00.000Z',
    updated_at: '2024-01-02T00:00:00.000Z'
  }
];

// Students data
const students = [
  {
    id: 1,
    full_name: 'أحمد محمد علي',
    nationality: 'سعودي',
    phone_number: '0501234567',
    class: 'الصف الأول',
    parent_phone_number: '0507654321',
    created_at: '2024-01-01T00:00:00.000Z',
    updated_at: '2024-01-01T00:00:00.000Z'
  },
  {
    id: 2,
    full_name: 'فاطمة أحمد محمد',
    nationality: 'سعودي',
    phone_number: '0501234568',
    class: 'الصف الثاني',
    parent_phone_number: '0507654322',
    created_at: '2024-01-01T00:00:00.000Z',
    updated_at: '2024-01-01T00:00:00.000Z'
  }
];

let currentToken = null;
let currentUser = null;

// Helper function to find user by email and password
function findUser(email, password) {
  return users.find(user => user.email === email && user.password === password);
}

// Helper function to generate token
function generateToken() {
  return 'mock_token_' + Math.random().toString(36).substr(2, 9);
}

// Routes
app.post('/api/login', (req, res) => {
  const { email, password } = req.body;
  
  const user = findUser(email, password);
  
  if (user) {
    currentToken = generateToken();
    currentUser = user;
    
    const { password: _, ...userWithoutPassword } = user;
    
    res.json({
      success: true,
      message: 'Login successful',
      token: currentToken,
      user: userWithoutPassword
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }
});

app.post('/api/register', (req, res) => {
  const { name, email, password, user_type, phone, department } = req.body;
  
  // Check if user already exists
  const existingUser = users.find(user => user.email === email);
  if (existingUser) {
    return res.status(400).json({
      success: false,
      message: 'User already exists'
    });
  }
  
  const newUser = {
    id: users.length + 1,
    name,
    email,
    password,
    user_type: user_type || 'student',
    phone,
    department,
    profile_image: null,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    is_active: true
  };
  
  users.push(newUser);
  
  currentToken = generateToken();
  currentUser = newUser;
  
  const { password: _, ...userWithoutPassword } = newUser;
  
  res.json({
    success: true,
    message: 'Registration successful',
    token: currentToken,
    user: userWithoutPassword
  });
});

app.post('/api/logout', (req, res) => {
  currentToken = null;
  currentUser = null;
  
  res.json({
    success: true,
    message: 'Logout successful'
  });
});

app.get('/api/notifications', (req, res) => {
  res.json({
    success: true,
    notifications: notifications
  });
});

app.get('/api/notifications/sent', (req, res) => {
  res.json({
    success: true,
    notifications: notifications
  });
});

app.post('/api/notifications', (req, res) => {
  const { title, message, type, recipient_type } = req.body;
  
  const newNotification = {
    id: notifications.length + 1,
    title,
    message,
    type: type || 'general',
    recipient_type,
    sender_id: currentUser?.id || 1,
    sender_name: currentUser?.name || 'Admin User',
    is_read: false,
    has_attachment: false,
    attachment_name: null,
    attachment_url: null,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  
  notifications.push(newNotification);
  
  res.json({
    success: true,
    message: 'Notification sent successfully',
    notification: newNotification
  });
});

app.put('/api/notifications/:id/read', (req, res) => {
  const notificationId = parseInt(req.params.id);
  const notification = notifications.find(n => n.id === notificationId);
  
  if (notification) {
    notification.is_read = true;
    notification.updated_at = new Date().toISOString();
    
    res.json({
      success: true,
      message: 'Notification marked as read'
    });
  } else {
    res.status(404).json({
      success: false,
      message: 'Notification not found'
    });
  }
});

app.put('/api/notifications/read-all', (req, res) => {
  notifications.forEach(notification => {
    notification.is_read = true;
    notification.updated_at = new Date().toISOString();
  });
  
  res.json({
    success: true,
    message: 'All notifications marked as read'
  });
});

app.get('/api/notifications/stats', (req, res) => {
  const totalNotifications = notifications.length;
  const unreadNotifications = notifications.filter(n => !n.is_read).length;
  
  res.json({
    success: true,
    stats: {
      total_notifications: totalNotifications,
      unread_notifications: unreadNotifications,
      read_notifications: totalNotifications - unreadNotifications
    }
  });
});

app.put('/api/profile', (req, res) => {
  if (!currentUser) {
    return res.status(401).json({
      success: false,
      message: 'Unauthorized'
    });
  }
  
  const { name, phone, department } = req.body;
  
  if (name) currentUser.name = name;
  if (phone) currentUser.phone = phone;
  if (department) currentUser.department = department;
  
  currentUser.updated_at = new Date().toISOString();
  
  const { password: _, ...userWithoutPassword } = currentUser;
  
  res.json({
    success: true,
    message: 'Profile updated successfully',
    user: userWithoutPassword
  });
});

app.get('/api/users', (req, res) => {
  const { user_type } = req.query;

  let filteredUsers = users;
  if (user_type) {
    filteredUsers = users.filter(user => user.user_type === user_type);
  }

  const usersWithoutPasswords = filteredUsers.map(({ password, ...user }) => user);

  res.json({
    success: true,
    users: usersWithoutPasswords
  });
});

// Students API endpoints
app.get('/api/students', (req, res) => {
  res.json({
    success: true,
    students: students
  });
});

app.post('/api/students', (req, res) => {
  const { full_name, nationality, phone_number, class: studentClass, parent_phone_number } = req.body;

  // Check if phone number already exists
  const existingStudent = students.find(student => student.phone_number === phone_number);
  if (existingStudent) {
    return res.status(400).json({
      success: false,
      message: 'رقم الهاتف موجود مسبقاً'
    });
  }

  const newStudent = {
    id: students.length + 1,
    full_name,
    nationality,
    phone_number,
    class: studentClass,
    parent_phone_number,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  students.push(newStudent);

  res.json({
    success: true,
    message: 'تم إضافة الطالب بنجاح',
    student: newStudent
  });
});

app.post('/api/students/bulk-import', (req, res) => {
  const { students: studentsData } = req.body;

  if (!Array.isArray(studentsData) || studentsData.length === 0) {
    return res.status(400).json({
      success: false,
      message: 'بيانات الطلاب غير صالحة'
    });
  }

  const importedStudents = [];
  const errors = [];

  studentsData.forEach((studentData, index) => {
    try {
      const { full_name, nationality, phone_number, class: studentClass, parent_phone_number } = studentData;

      // Validate required fields
      if (!full_name || !nationality || !phone_number || !studentClass || !parent_phone_number) {
        errors.push(`الصف ${index + 1}: بيانات ناقصة`);
        return;
      }

      // Check if phone number already exists
      const existingStudent = students.find(student => student.phone_number === phone_number);
      if (existingStudent) {
        errors.push(`الصف ${index + 1}: رقم الهاتف ${phone_number} موجود مسبقاً`);
        return;
      }

      const newStudent = {
        id: students.length + importedStudents.length + 1,
        full_name,
        nationality,
        phone_number,
        class: studentClass,
        parent_phone_number,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      students.push(newStudent);
      importedStudents.push(newStudent);
    } catch (error) {
      errors.push(`الصف ${index + 1}: خطأ في المعالجة`);
    }
  });

  res.json({
    success: true,
    message: `تم استيراد ${importedStudents.length} طالب`,
    imported_count: importedStudents.length,
    error_count: errors.length,
    errors: errors,
    students: importedStudents
  });
});

app.delete('/api/students/:id', (req, res) => {
  const studentId = parseInt(req.params.id);
  const studentIndex = students.findIndex(s => s.id === studentId);

  if (studentIndex === -1) {
    return res.status(404).json({
      success: false,
      message: 'الطالب غير موجود'
    });
  }

  students.splice(studentIndex, 1);

  res.json({
    success: true,
    message: 'تم حذف الطالب بنجاح'
  });
});

app.listen(port, () => {
  console.log(`Mock API server running at http://localhost:${port}`);
  console.log('\nTest Credentials:');
  console.log('Admin: <EMAIL> / admin123');
  console.log('Teacher: <EMAIL> / teacher123');
  console.log('Student: <EMAIL> / student123');
});
