# تشخيص مشكلة الاتصال بالخادم

## المشاكل المكتشفة:

### 1. **تضارب في مسارات API**
- **Flutter يرسل إلى**: `/api/login`
- **Laravel يتوقع**: `/api/auth/login`

### 2. **عناوين URL محتملة**
- `http://localhost:8000` (php artisan serve)
- `http://127.0.0.1:8000` (بديل localhost)
- `http://notysend.test` (Laragon)
- `http://localhost/notysend/public` (XAMPP/WAMP)

## الحلول المطبقة:

### ✅ تم تحديث Flutter API Service
تم تحديث المسارات التالية في `lib/services/api_service.dart`:
- `login`: `/api/login` → `/api/auth/login`
- `register`: `/api/register` → `/api/auth/register`
- `logout`: `/api/logout` → `/api/auth/logout`

## خطوات التشخيص:

### 1. **تحقق من تشغيل Laravel**
```bash
# في مجلد Laravel
php artisan serve
```

### 2. **اختبر الاتصال يدوياً**
```bash
# اختبار Health Check
curl http://localhost:8000/api/health

# اختبار Test Route
curl http://localhost:8000/api/test

# اختبار Login
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'
```

### 3. **تشغيل اختبار Dart**
```bash
dart test_api_connection.dart
```

## مشاكل محتملة أخرى:

### 1. **CORS Issues**
إذا كان هناك خطأ CORS، أضف إلى `config/cors.php`:
```php
'paths' => ['api/*'],
'allowed_methods' => ['*'],
'allowed_origins' => ['*'],
'allowed_headers' => ['*'],
```

### 2. **Laravel غير مُشغل**
تأكد من تشغيل Laravel:
```bash
cd /path/to/laravel/project
php artisan serve
```

### 3. **قاعدة البيانات غير متصلة**
تحقق من `.env` في Laravel:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=notysend
DB_USERNAME=root
DB_PASSWORD=
```

### 4. **مشكلة في الشبكة**
- تأكد من أن الجهاز متصل بالإنترنت
- جرب `127.0.0.1` بدلاً من `localhost`
- تأكد من عدم حجب Firewall للمنفذ 8000

## التحقق من الحالة:

### ✅ Laravel API Routes
```php
// routes/api.php
Route::post('/auth/login', [AuthController::class, 'login']);
Route::post('/auth/register', [AuthController::class, 'register']);
Route::middleware('auth:sanctum')->group(function () {
    Route::post('/auth/logout', [AuthController::class, 'logout']);
    // ... باقي المسارات
});
```

### ✅ Flutter API Service
```dart
// lib/services/api_service.dart
static const String baseUrl = 'http://localhost:8000/api';

// تم تحديث المسارات:
Uri.parse('$baseUrl/auth/login')
Uri.parse('$baseUrl/auth/register')
Uri.parse('$baseUrl/auth/logout')
```

## الخطوات التالية:

1. **تشغيل Laravel**: `php artisan serve`
2. **تشغيل اختبار الاتصال**: `dart test_api_connection.dart`
3. **تشغيل Flutter**: `flutter run`
4. **اختبار تسجيل الدخول** في التطبيق

## معلومات إضافية:

### بيانات اختبار (إذا كانت موجودة):
- **Admin**: <EMAIL> / admin123
- **Teacher**: <EMAIL> / teacher123
- **Student**: <EMAIL> / student123
