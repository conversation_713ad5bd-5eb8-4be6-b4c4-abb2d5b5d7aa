import 'package:flutter/material.dart';
import 'lib/services/api_service.dart';
import 'lib/models/student_model.dart';
import 'lib/providers/student_provider.dart';

void main() {
  runApp(ExcelImportTestApp());
}

class ExcelImportTestApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Excel Import Test',
      home: ExcelImportTestScreen(),
    );
  }
}

class ExcelImportTestScreen extends StatefulWidget {
  @override
  _ExcelImportTestScreenState createState() => _ExcelImportTestScreenState();
}

class _ExcelImportTestScreenState extends State<ExcelImportTestScreen> {
  String _result = 'اضغط على الأزرار لاختبار ميزة استيراد Excel';
  bool _loading = false;
  final StudentProvider _studentProvider = StudentProvider();

  Future<void> _testGetStudents() async {
    setState(() {
      _loading = true;
      _result = 'جاري جلب قائمة الطلاب...';
    });

    try {
      await _studentProvider.refreshStudents();
      
      setState(() {
        _result = 'قائمة الطلاب:\n';
        for (var student in _studentProvider.students) {
          _result += '• ${student.fullName} - ${student.phoneNumber}\n';
        }
        if (_studentProvider.students.isEmpty) {
          _result += 'لا توجد طلاب في النظام';
        }
        _loading = false;
      });
    } catch (e) {
      setState(() {
        _result = 'خطأ في جلب الطلاب:\n$e';
        _loading = false;
      });
    }
  }

  Future<void> _testAddSingleStudent() async {
    setState(() {
      _loading = true;
      _result = 'جاري إضافة طالب واحد...';
    });

    try {
      final student = Student(
        fullName: 'طالب تجريبي ${DateTime.now().millisecond}',
        nationality: 'سعودي',
        phoneNumber: '050${DateTime.now().millisecond}',
        studentClass: 'الصف التجريبي',
        parentPhoneNumber: '050${DateTime.now().millisecond + 1000}',
        password: 'student123',
      );
      
      final success = await _studentProvider.addStudent(student);
      
      setState(() {
        if (success) {
          _result = 'تم إضافة الطالب بنجاح:\n${student.fullName}';
        } else {
          _result = 'فشل في إضافة الطالب:\n${_studentProvider.error}';
        }
        _loading = false;
      });
    } catch (e) {
      setState(() {
        _result = 'خطأ في إضافة الطالب:\n$e';
        _loading = false;
      });
    }
  }

  Future<void> _testBulkImport() async {
    setState(() {
      _loading = true;
      _result = 'جاري اختبار الاستيراد بالجملة...';
    });

    try {
      final students = [
        Student(
          fullName: 'أحمد محمد الاختبار',
          nationality: 'سعودي',
          phoneNumber: '**********',
          studentClass: 'الصف الأول',
          parentPhoneNumber: '**********',
          password: 'student123',
        ),
        Student(
          fullName: 'فاطمة علي الاختبار',
          nationality: 'مصري',
          phoneNumber: '**********',
          studentClass: 'الصف الثاني',
          parentPhoneNumber: '**********',
          password: 'student123',
        ),
        Student(
          fullName: 'محمد أحمد الاختبار',
          nationality: 'أردني',
          phoneNumber: '**********',
          studentClass: 'الصف الثالث',
          parentPhoneNumber: '**********',
          password: 'student123',
        ),
      ];
      
      final result = await _studentProvider.bulkImportStudents(students);
      
      setState(() {
        if (result['success'] == true) {
          _result = 'نجح الاستيراد بالجملة!\n'
                   'تم استيراد: ${result['imported_count']} طالب\n'
                   'فشل: ${result['error_count']} طالب\n'
                   'الرسالة: ${result['message']}';
          
          if (result['errors'] != null && result['errors'].isNotEmpty) {
            _result += '\n\nالأخطاء:\n';
            for (var error in result['errors']) {
              _result += '• $error\n';
            }
          }
        } else {
          _result = 'فشل الاستيراد بالجملة:\n${result['message']}';
        }
        _loading = false;
      });
    } catch (e) {
      setState(() {
        _result = 'خطأ في الاستيراد بالجملة:\n$e';
        _loading = false;
      });
    }
  }

  Future<void> _testDuplicatePhones() async {
    setState(() {
      _loading = true;
      _result = 'جاري اختبار الأرقام المكررة...';
    });

    try {
      final students = [
        Student(
          fullName: 'طالب برقم مكرر 1',
          nationality: 'سعودي',
          phoneNumber: '**********', // رقم موجود مسبقاً
          studentClass: 'الصف الأول',
          parentPhoneNumber: '**********',
          password: 'student123',
        ),
        Student(
          fullName: 'طالب برقم جديد',
          nationality: 'مصري',
          phoneNumber: '**********',
          studentClass: 'الصف الثاني',
          parentPhoneNumber: '**********',
          password: 'student123',
        ),
      ];
      
      final result = await _studentProvider.bulkImportStudents(students);
      
      setState(() {
        _result = 'نتيجة اختبار الأرقام المكررة:\n'
                 'تم استيراد: ${result['imported_count']} طالب\n'
                 'فشل: ${result['error_count']} طالب\n'
                 'الرسالة: ${result['message']}';
        
        if (result['errors'] != null && result['errors'].isNotEmpty) {
          _result += '\n\nالأخطاء المتوقعة:\n';
          for (var error in result['errors']) {
            _result += '• $error\n';
          }
        }
        _loading = false;
      });
    } catch (e) {
      setState(() {
        _result = 'خطأ في اختبار الأرقام المكررة:\n$e';
        _loading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('اختبار استيراد Excel'),
        backgroundColor: Colors.green,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'اختبار ميزة استيراد الطلاب من Excel',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 20),
            
            ElevatedButton(
              onPressed: _loading ? null : _testGetStudents,
              child: Text('1. جلب قائمة الطلاب'),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
            ),
            
            SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: _loading ? null : _testAddSingleStudent,
              child: Text('2. إضافة طالب واحد'),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
            ),
            
            SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: _loading ? null : _testBulkImport,
              child: Text('3. اختبار الاستيراد بالجملة'),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            ),
            
            SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: _loading ? null : _testDuplicatePhones,
              child: Text('4. اختبار الأرقام المكررة'),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            ),
            
            SizedBox(height: 20),
            
            if (_loading)
              Center(child: CircularProgressIndicator()),
            
            Expanded(
              child: Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _result,
                    style: TextStyle(fontFamily: 'monospace'),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
