import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/notification_provider.dart';
import '../../utils/app_theme.dart';
import '../notifications/send_notification_screen.dart';
import '../notifications/notifications_list_screen.dart';
import '../profile/profile_screen.dart';
import '../auth/login_screen.dart';
import '../admin/students_list_screen.dart';
import '../admin/add_student_screen.dart';
import '../admin/import_students_screen.dart';
import '../admin/api_test_screen.dart';

class AdminDashboard extends StatefulWidget {
  const AdminDashboard({super.key});

  @override
  State<AdminDashboard> createState() => _AdminDashboardState();
}

class _AdminDashboardState extends State<AdminDashboard> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const AdminHomeScreen(),
    const NotificationsListScreen(),
    const SendNotificationScreen(),
    const ProfileScreen(),
  ];

  @override
  void initState() {
    super.initState();
    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<NotificationProvider>(
        context,
        listen: false,
      ).getSentNotifications();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        type: BottomNavigationBarType.fixed,
        selectedItemColor: AppTheme.primaryGreen,
        unselectedItemColor: AppTheme.textSecondary,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'الرئيسية',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.notifications),
            label: 'الإشعارات',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.send), label: 'إرسال إشعار'),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'الملف الشخصي',
          ),
        ],
      ),
    );
  }
}

class AdminHomeScreen extends StatelessWidget {
  const AdminHomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة تحكم المدير'),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () => _logout(context),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome card
            Consumer<AuthProvider>(
              builder: (context, authProvider, child) {
                return Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        CircleAvatar(
                          radius: 30,
                          backgroundColor: AppTheme.primaryGreen,
                          child: Text(
                            authProvider.user?.initials ?? 'A',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'مرحباً، ${authProvider.user?.name ?? 'المدير'}',
                                style: Theme.of(context).textTheme.titleLarge,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'مدير النظام',
                                style: Theme.of(context).textTheme.bodyMedium
                                    ?.copyWith(color: AppTheme.textSecondary),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 20),

            // Statistics cards
            const Text(
              'إحصائيات سريعة',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
            ),

            const SizedBox(height: 16),

            Consumer<NotificationProvider>(
              builder: (context, notificationProvider, child) {
                return GridView.count(
                  crossAxisCount: 2,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  children: [
                    _buildStatCard(
                      'الإشعارات المرسلة',
                      '${notificationProvider.sentNotifications.length}',
                      Icons.send,
                      AppTheme.primaryGreen,
                    ),
                    _buildStatCard(
                      'المستخدمين النشطين',
                      '0', // This would come from API
                      Icons.people,
                      AppTheme.infoColor,
                    ),
                    _buildStatCard(
                      'الطلاب',
                      '0', // This would come from API
                      Icons.school,
                      AppTheme.successColor,
                    ),
                    _buildStatCard(
                      'المعلمين',
                      '0', // This would come from API
                      Icons.person_outline,
                      AppTheme.warningColor,
                    ),
                  ],
                );
              },
            ),

            const SizedBox(height: 30),

            // Quick actions
            const Text(
              'إجراءات سريعة',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
            ),

            const SizedBox(height: 16),

            _buildQuickActionCard(
              context,
              'إرسال إشعار جديد',
              'إرسال إشعار للطلاب أو المعلمين',
              Icons.notification_add,
              () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const SendNotificationScreen(),
                  ),
                );
              },
            ),

            const SizedBox(height: 12),

            _buildQuickActionCard(
              context,
              'عرض الإشعارات المرسلة',
              'مراجعة جميع الإشعارات التي تم إرسالها',
              Icons.history,
              () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const NotificationsListScreen(),
                  ),
                );
              },
            ),

            const SizedBox(height: 12),

            _buildQuickActionCard(
              context,
              'إدارة الطلاب',
              'عرض وإدارة قائمة الطلاب',
              Icons.school,
              () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const StudentsListScreen(),
                  ),
                );
              },
            ),

            const SizedBox(height: 12),

            _buildQuickActionCard(
              context,
              'إضافة طالب جديد',
              'إضافة طالب جديد إلى النظام',
              Icons.person_add,
              () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const AddStudentScreen(),
                  ),
                );
              },
            ),

            const SizedBox(height: 12),

            _buildQuickActionCard(
              context,
              'استيراد الطلاب من Excel',
              'استيراد عدة طلاب من ملف Excel',
              Icons.file_upload,
              () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const ImportStudentsScreen(),
                  ),
                );
              },
            ),

            const SizedBox(height: 12),

            _buildQuickActionCard(
              context,
              'اختبار الاتصال بالخادم',
              'فحص الاتصال مع خادم Laravel',
              Icons.wifi_find,
              () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const ApiTestScreen(),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 40, color: color),
            const SizedBox(height: 12),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionCard(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Card(
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppTheme.primaryGreen.withValues(alpha: 0.1),
          child: Icon(icon, color: AppTheme.primaryGreen),
        ),
        title: Text(title, style: const TextStyle(fontWeight: FontWeight.w600)),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: onTap,
      ),
    );
  }

  void _logout(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Provider.of<AuthProvider>(context, listen: false).logout();
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (context) => const LoginScreen()),
              );
            },
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }
}
