# 📋 دليل قوالب Excel لاستيراد الطلاب

## 🎯 **صفوف العناوين المطلوبة:**

### الترتيب الصحيح للأعمدة:

| العمود | العنوان | مطلوب؟ | مثال | ملاحظات |
|--------|---------|---------|-------|----------|
| **A** | الاسم الكامل | ✅ نعم | أحمد محمد علي السعدي | الاسم الثلاثي أو الرباعي |
| **B** | الجنسية | ✅ نعم | سعودي | الجنسية بالعربية |
| **C** | رقم الهاتف | ✅ نعم | 0501111111 | يجب أن يكون فريد |
| **D** | الصف | ✅ نعم | الصف الأول الابتدائي | اسم الصف كاملاً |
| **E** | رقم هاتف ولي الأمر | ✅ نعم | 0507111111 | مختلف عن رقم الطالب |

## 📄 **القوالب المتاحة:**

### 1. **قالب مع بيانات تجريبية** 📊
```csv
الاسم الكامل,الجنسية,رقم الهاتف,الصف,رقم هاتف ولي الأمر
أحمد محمد علي السعدي,سعودي,0501111111,الصف الأول الابتدائي,0507111111
فاطمة أحمد محمد الأحمدي,سعودي,0501222222,الصف الثاني الابتدائي,0507222222
محمد علي أحمد المصري,مصري,0501333333,الصف الثالث الابتدائي,0507333333
سارة خالد أحمد الكويتي,كويتي,0501444444,الصف الرابع الابتدائي,0507444444
عبدالله يوسف محمد الأردني,أردني,0501555555,الصف الخامس الابتدائي,0507555555
نورا حسن علي اللبناني,لبناني,0501666666,الصف السادس الابتدائي,0507666666
خالد عبدالرحمن أحمد السوري,سوري,0501777777,الصف الأول المتوسط,0507777777
مريم عبدالله محمد العراقي,عراقي,0501888888,الصف الثاني المتوسط,0507888888
يوسف أحمد علي الفلسطيني,فلسطيني,0501999999,الصف الثالث المتوسط,0507999999
هند محمد خالد اليمني,يمني,0502111111,الصف الأول الثانوي,0508111111
```

**الاستخدام**: مثالي للاختبار وفهم التنسيق المطلوب

### 2. **قالب فارغ** 📝
```csv
الاسم الكامل,الجنسية,رقم الهاتف,الصف,رقم هاتف ولي الأمر
,,,,
,,,,
,,,,
,,,,
,,,,
```

**الاستخدام**: للبدء من الصفر وإدخال بيانات حقيقية

### 3. **قالب مع التعليمات** 📚
```csv
الاسم الكامل,الجنسية,رقم الهاتف,الصف,رقم هاتف ولي الأمر
# اكتب الاسم الكامل,# مثال: سعودي,# مثال: 0501234567,# مثال: الصف الأول,# مثال: 0507654321
أحمد محمد علي,سعودي,0501111111,الصف الأول الابتدائي,0507111111
# احذف الصفوف التي تبدأ بـ #,,,,,
,,,,
```

**الاستخدام**: للمبتدئين مع تعليمات مدمجة

## 🔧 **كيفية الحصول على القوالب:**

### من التطبيق:
1. **اذهب لشاشة استيراد الطلاب**
2. **اضغط على أحد أزرار التحميل**:
   - 🟢 **قالب مع بيانات** (للاختبار)
   - 🔵 **قالب فارغ** (للاستخدام الفعلي)
3. **انسخ البيانات من النافذة المنبثقة**
4. **الصقها في Excel أو Google Sheets**
5. **احفظ الملف بصيغة .xlsx**

### من الملفات:
- `assets/students_template.csv` - قالب مع بيانات
- `assets/students_template_empty.csv` - قالب فارغ
- `assets/students_template_with_instructions.csv` - قالب مع تعليمات

## ⚠️ **قواعد مهمة:**

### 1. **ترتيب الأعمدة:**
- يجب أن تكون الأعمدة بنفس الترتيب المذكور
- الصف الأول = العناوين
- البيانات تبدأ من الصف الثاني

### 2. **الحقول المطلوبة:**
- **جميع الحقول الخمسة مطلوبة**
- لا يمكن ترك أي حقل فارغ
- الصفوف الفارغة سيتم تجاهلها

### 3. **قيود البيانات:**
- **رقم الهاتف**: يجب أن يكون فريد (غير مكرر)
- **تنسيق الملف**: .xlsx أو .xls فقط
- **الترميز**: UTF-8 للنصوص العربية

## 📱 **أمثلة على البيانات الصحيحة:**

### أسماء صحيحة:
- ✅ أحمد محمد علي
- ✅ فاطمة أحمد محمد السعدي
- ✅ عبدالله يوسف محمد الأحمدي

### جنسيات صحيحة:
- ✅ سعودي، مصري، أردني، كويتي
- ✅ لبناني، سوري، عراقي، فلسطيني
- ✅ يمني، مغربي، تونسي، جزائري

### أرقام هواتف صحيحة:
- ✅ 0501234567 (يبدأ بـ 05)
- ✅ 0551234567 (يبدأ بـ 055)
- ✅ 0561234567 (يبدأ بـ 056)

### أسماء صفوف صحيحة:
- ✅ الصف الأول الابتدائي
- ✅ الصف الثاني المتوسط
- ✅ الصف الثالث الثانوي
- ✅ الصف التمهيدي

## 🚫 **أخطاء شائعة يجب تجنبها:**

### ❌ أخطاء في البيانات:
- ترك حقول فارغة
- تكرار أرقام الهواتف
- استخدام أحرف خاصة في الأرقام
- أرقام هواتف غير صحيحة

### ❌ أخطاء في التنسيق:
- تغيير ترتيب الأعمدة
- حذف صف العناوين
- استخدام تنسيق ملف خاطئ
- إضافة أعمدة إضافية

## 🎯 **نصائح للنجاح:**

### 1. **قبل البدء:**
- حمل القالب المناسب
- تأكد من فهم التنسيق المطلوب
- جهز قائمة الطلاب مسبقاً

### 2. **أثناء الإدخال:**
- تأكد من عدم تكرار أرقام الهواتف
- استخدم أسماء واضحة ومفهومة
- تحقق من صحة البيانات قبل الحفظ

### 3. **قبل الرفع:**
- احفظ الملف بصيغة .xlsx
- تأكد من وجود جميع البيانات المطلوبة
- اختبر الملف بعدد قليل من الطلاب أولاً

## 📞 **الدعم:**

إذا واجهت أي مشاكل:
1. تأكد من استخدام القالب الصحيح
2. تحقق من ترتيب الأعمدة
3. تأكد من عدم وجود حقول فارغة
4. جرب القالب مع بيانات تجريبية أولاً

---

**🎉 مع هذه القوالب، ستتمكن من استيراد الطلاب بسهولة وسرعة!**
