import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:excel/excel.dart';
import '../../providers/student_provider.dart';
import '../../models/student_model.dart';
import '../../utils/app_theme.dart';

class ImportStudentsScreen extends StatefulWidget {
  const ImportStudentsScreen({super.key});

  @override
  State<ImportStudentsScreen> createState() => _ImportStudentsScreenState();
}

class _ImportStudentsScreenState extends State<ImportStudentsScreen> {
  List<Student> _studentsToImport = [];
  bool _isLoading = false;
  String? _errorMessage;
  String? _successMessage;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('استيراد الطلاب من Excel'),
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: _downloadTemplate,
            tooltip: 'تحميل قالب Excel',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Instructions Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info, color: AppTheme.infoColor),
                        const SizedBox(width: 8),
                        const Text(
                          'تعليمات الاستيراد',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      '1. حمل قالب Excel بالضغط على أيقونة التحميل أعلاه\n'
                      '2. املأ البيانات في القالب:\n'
                      '   • الاسم الكامل (مطلوب)\n'
                      '   • الجنسية (مطلوب)\n'
                      '   • رقم الهاتف (مطلوب - يجب أن يكون فريد)\n'
                      '   • الصف (مطلوب)\n'
                      '   • رقم هاتف ولي الأمر (مطلوب)\n'
                      '3. احفظ الملف واختره للاستيراد\n'
                      '4. راجع البيانات قبل الحفظ النهائي',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // File Selection
            ElevatedButton.icon(
              onPressed: _isLoading ? null : _pickExcelFile,
              icon: const Icon(Icons.file_upload),
              label: const Text('اختيار ملف Excel'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryGreen,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Error Message
            if (_errorMessage != null)
              Card(
                color: AppTheme.errorColor.withOpacity(0.1),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(Icons.error, color: AppTheme.errorColor),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: TextStyle(color: AppTheme.errorColor),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            
            // Success Message
            if (_successMessage != null)
              Card(
                color: AppTheme.successColor.withOpacity(0.1),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(Icons.check_circle, color: AppTheme.successColor),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _successMessage!,
                          style: TextStyle(color: AppTheme.successColor),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            
            // Loading Indicator
            if (_isLoading)
              const Card(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Row(
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(width: 16),
                      Text('جاري معالجة الملف...'),
                    ],
                  ),
                ),
              ),
            
            // Students Preview
            if (_studentsToImport.isNotEmpty) ...[
              const SizedBox(height: 20),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.preview, color: AppTheme.infoColor),
                          const SizedBox(width: 8),
                          Text(
                            'معاينة البيانات (${_studentsToImport.length} طالب)',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        height: 300,
                        child: ListView.builder(
                          itemCount: _studentsToImport.length,
                          itemBuilder: (context, index) {
                            final student = _studentsToImport[index];
                            return Card(
                              margin: const EdgeInsets.only(bottom: 8),
                              child: ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: AppTheme.primaryGreen,
                                  child: Text('${index + 1}'),
                                ),
                                title: Text(student.fullName),
                                subtitle: Text(
                                  'الجنسية: ${student.nationality}\n'
                                  'الهاتف: ${student.phoneNumber}\n'
                                  'الصف: ${student.studentClass}',
                                ),
                                isThreeLine: true,
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _importStudents,
                              icon: const Icon(Icons.save),
                              label: const Text('حفظ جميع الطلاب'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppTheme.successColor,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(vertical: 12),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          ElevatedButton.icon(
                            onPressed: _clearData,
                            icon: const Icon(Icons.clear),
                            label: const Text('مسح'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppTheme.errorColor,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _downloadTemplate() async {
    try {
      // Create Excel template
      final excel = Excel.createExcel();
      final sheet = excel['الطلاب'];
      
      // Add headers
      sheet.cell(CellIndex.indexByString('A1')).value = const TextCellValue('الاسم الكامل');
      sheet.cell(CellIndex.indexByString('B1')).value = const TextCellValue('الجنسية');
      sheet.cell(CellIndex.indexByString('C1')).value = const TextCellValue('رقم الهاتف');
      sheet.cell(CellIndex.indexByString('D1')).value = const TextCellValue('الصف');
      sheet.cell(CellIndex.indexByString('E1')).value = const TextCellValue('رقم هاتف ولي الأمر');
      
      // Add sample data
      sheet.cell(CellIndex.indexByString('A2')).value = const TextCellValue('أحمد محمد علي');
      sheet.cell(CellIndex.indexByString('B2')).value = const TextCellValue('سعودي');
      sheet.cell(CellIndex.indexByString('C2')).value = const TextCellValue('0501234567');
      sheet.cell(CellIndex.indexByString('D2')).value = const TextCellValue('الصف الأول');
      sheet.cell(CellIndex.indexByString('E2')).value = const TextCellValue('0507654321');
      
      // Show download dialog
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إنشاء قالب Excel. يمكنك نسخ البيانات وحفظها في ملف Excel.'),
          backgroundColor: AppTheme.successColor,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في إنشاء القالب: $e'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
    }
  }

  Future<void> _pickExcelFile() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
        _successMessage = null;
      });

      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        if (file.bytes != null) {
          await _processExcelFile(file.bytes!);
        } else {
          throw Exception('لا يمكن قراءة الملف');
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في اختيار الملف: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _processExcelFile(Uint8List bytes) async {
    try {
      final excel = Excel.decodeBytes(bytes);
      final sheet = excel.tables.values.first;
      
      final students = <Student>[];
      
      // Skip header row (index 0)
      for (int i = 1; i < sheet.maxRows; i++) {
        final row = sheet.rows[i];
        
        // Check if row has data
        if (row.isEmpty || row.every((cell) => cell?.value == null)) {
          continue;
        }
        
        try {
          final fullName = row[0]?.value?.toString().trim() ?? '';
          final nationality = row[1]?.value?.toString().trim() ?? '';
          final phoneNumber = row[2]?.value?.toString().trim() ?? '';
          final studentClass = row[3]?.value?.toString().trim() ?? '';
          final parentPhone = row[4]?.value?.toString().trim() ?? '';
          
          if (fullName.isEmpty || nationality.isEmpty || 
              phoneNumber.isEmpty || studentClass.isEmpty || 
              parentPhone.isEmpty) {
            continue; // Skip incomplete rows
          }
          
          students.add(Student(
            fullName: fullName,
            nationality: nationality,
            phoneNumber: phoneNumber,
            studentClass: studentClass,
            parentPhoneNumber: parentPhone,
            password: 'student123', // Default password
          ));
        } catch (e) {
          // Skip invalid rows
          continue;
        }
      }
      
      if (students.isEmpty) {
        throw Exception('لم يتم العثور على بيانات صالحة في الملف');
      }
      
      setState(() {
        _studentsToImport = students;
        _successMessage = 'تم تحميل ${students.length} طالب من الملف';
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في معالجة الملف: $e';
      });
    }
  }

  Future<void> _importStudents() async {
    if (_studentsToImport.isEmpty) return;
    
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
        _successMessage = null;
      });
      
      final studentProvider = Provider.of<StudentProvider>(context, listen: false);
      
      int successCount = 0;
      int errorCount = 0;
      
      for (final student in _studentsToImport) {
        try {
          await studentProvider.addStudent(student);
          successCount++;
        } catch (e) {
          errorCount++;
        }
      }
      
      setState(() {
        if (errorCount == 0) {
          _successMessage = 'تم استيراد جميع الطلاب بنجاح ($successCount طالب)';
          _studentsToImport.clear();
        } else {
          _successMessage = 'تم استيراد $successCount طالب، فشل $errorCount طالب';
        }
      });
      
      // Refresh students list
      await studentProvider.refreshStudents();
      
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في استيراد الطلاب: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _clearData() {
    setState(() {
      _studentsToImport.clear();
      _errorMessage = null;
      _successMessage = null;
    });
  }
}
