import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/student_provider.dart';
import '../../models/student_model.dart';
import '../../utils/app_theme.dart';

class ImportStudentsScreen extends StatefulWidget {
  const ImportStudentsScreen({super.key});

  @override
  State<ImportStudentsScreen> createState() => _ImportStudentsScreenState();
}

class _ImportStudentsScreenState extends State<ImportStudentsScreen> {
  List<Student> _studentsToImport = [];
  bool _isLoading = false;
  String? _errorMessage;
  String? _successMessage;

  // Sample data for demonstration
  final List<Map<String, String>> _sampleData = [
    {
      'full_name': 'أحمد محمد علي',
      'nationality': 'سعودي',
      'phone_number': '**********',
      'class': 'الصف الأول',
      'parent_phone_number': '**********',
    },
    {
      'full_name': 'فاطمة أحمد محمد',
      'nationality': 'مصري',
      'phone_number': '**********',
      'class': 'الصف الثاني',
      'parent_phone_number': '**********',
    },
    {
      'full_name': 'محمد علي أحمد',
      'nationality': 'أردني',
      'phone_number': '0503333333',
      'class': 'الصف الثالث',
      'parent_phone_number': '0509999999',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('استيراد الطلاب (تجريبي)'),
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Instructions Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info, color: AppTheme.infoColor),
                        const SizedBox(width: 8),
                        const Text(
                          'نسخة تجريبية - استيراد الطلاب',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      'هذه نسخة تجريبية من ميزة استيراد الطلاب.\n'
                      'يمكنك:\n'
                      '• تحميل بيانات تجريبية للاختبار\n'
                      '• مراجعة البيانات قبل الحفظ\n'
                      '• حفظ الطلاب في النظام\n\n'
                      'في النسخة الكاملة ستتمكن من رفع ملفات Excel.',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Template Download Buttons
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.file_download, color: AppTheme.infoColor),
                        const SizedBox(width: 8),
                        const Text(
                          'تحميل قوالب Excel',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () => _downloadTemplate('sample'),
                            icon: const Icon(Icons.file_copy),
                            label: const Text('قالب مع بيانات'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () => _downloadTemplate('empty'),
                            icon: const Icon(Icons.file_present),
                            label: const Text('قالب فارغ'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Load Sample Data Button
            ElevatedButton.icon(
              onPressed: _isLoading ? null : _loadSampleData,
              icon: const Icon(Icons.download),
              label: const Text('تحميل بيانات تجريبية'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryGreen,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),

            const SizedBox(height: 20),

            // Error Message
            if (_errorMessage != null)
              Card(
                color: AppTheme.errorColor.withValues(alpha: 0.1),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(Icons.error, color: AppTheme.errorColor),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: TextStyle(color: AppTheme.errorColor),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            // Success Message
            if (_successMessage != null)
              Card(
                color: AppTheme.successColor.withValues(alpha: 0.1),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(Icons.check_circle, color: AppTheme.successColor),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _successMessage!,
                          style: TextStyle(color: AppTheme.successColor),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            // Loading Indicator
            if (_isLoading)
              const Card(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Row(
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(width: 16),
                      Text('جاري معالجة البيانات...'),
                    ],
                  ),
                ),
              ),

            // Students Preview
            if (_studentsToImport.isNotEmpty) ...[
              const SizedBox(height: 20),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.preview, color: AppTheme.infoColor),
                          const SizedBox(width: 8),
                          Text(
                            'معاينة البيانات (${_studentsToImport.length} طالب)',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        height: 300,
                        child: ListView.builder(
                          itemCount: _studentsToImport.length,
                          itemBuilder: (context, index) {
                            final student = _studentsToImport[index];
                            return Card(
                              margin: const EdgeInsets.only(bottom: 8),
                              child: ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: AppTheme.primaryGreen,
                                  child: Text('${index + 1}'),
                                ),
                                title: Text(student.fullName),
                                subtitle: Text(
                                  'الجنسية: ${student.nationality}\n'
                                  'الهاتف: ${student.phoneNumber}\n'
                                  'الصف: ${student.studentClass}',
                                ),
                                isThreeLine: true,
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _importStudents,
                              icon: const Icon(Icons.save),
                              label: const Text('حفظ جميع الطلاب'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppTheme.successColor,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          ElevatedButton.icon(
                            onPressed: _clearData,
                            icon: const Icon(Icons.clear),
                            label: const Text('مسح'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppTheme.errorColor,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _loadSampleData() {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = null;
    });

    // Simulate loading delay
    Future.delayed(const Duration(seconds: 1), () {
      final students = _sampleData
          .map(
            (data) => Student(
              fullName: data['full_name']!,
              nationality: data['nationality']!,
              phoneNumber: data['phone_number']!,
              studentClass: data['class']!,
              parentPhoneNumber: data['parent_phone_number']!,
              password: 'student123',
            ),
          )
          .toList();

      setState(() {
        _studentsToImport = students;
        _successMessage =
            'تم تحميل ${students.length} طالب من البيانات التجريبية';
        _isLoading = false;
      });
    });
  }

  Future<void> _importStudents() async {
    if (_studentsToImport.isEmpty) return;

    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
        _successMessage = null;
      });

      final studentProvider = Provider.of<StudentProvider>(
        context,
        listen: false,
      );

      final result = await studentProvider.bulkImportStudents(
        _studentsToImport,
      );

      setState(() {
        if (result['success'] == true) {
          _successMessage =
              'تم استيراد ${result['imported_count']} طالب بنجاح!';
          if (result['error_count'] > 0) {
            _successMessage =
                '$_successMessage\nفشل في استيراد ${result['error_count']} طالب.';
          }
          _studentsToImport.clear();
        } else {
          _errorMessage = 'فشل في استيراد الطلاب: ${result['message']}';
        }
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في استيراد الطلاب: $e';
        _isLoading = false;
      });
    }
  }

  void _downloadTemplate(String type) {
    String templateData = '';
    String fileName = '';

    switch (type) {
      case 'sample':
        templateData =
            '''الاسم الكامل,الجنسية,رقم الهاتف,الصف,رقم هاتف ولي الأمر
أحمد محمد علي السعدي,سعودي,**********,الصف الأول الابتدائي,0507111111
فاطمة أحمد محمد الأحمدي,سعودي,0501222222,الصف الثاني الابتدائي,0507222222
محمد علي أحمد المصري,مصري,0501333333,الصف الثالث الابتدائي,0507333333
سارة خالد أحمد الكويتي,كويتي,0501444444,الصف الرابع الابتدائي,0507444444
عبدالله يوسف محمد الأردني,أردني,0501555555,الصف الخامس الابتدائي,0507555555''';
        fileName = 'students_template_with_data.csv';
        break;
      case 'empty':
        templateData =
            '''الاسم الكامل,الجنسية,رقم الهاتف,الصف,رقم هاتف ولي الأمر
,,,,
,,,,
,,,,
,,,,
,,,,''';
        fileName = 'students_template_empty.csv';
        break;
      case 'instructions':
        templateData =
            '''الاسم الكامل,الجنسية,رقم الهاتف,الصف,رقم هاتف ولي الأمر
# اكتب الاسم الكامل,# مثال: سعودي,# مثال: 0501234567,# مثال: الصف الأول,# مثال: 0507654321
أحمد محمد علي,سعودي,**********,الصف الأول الابتدائي,0507111111
# احذف الصفوف التي تبدأ بـ #,,,,,
,,,,''';
        fileName = 'students_template_with_instructions.csv';
        break;
    }

    // Show template data in a dialog for copying
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('قالب Excel - $fileName'),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'انسخ البيانات التالية والصقها في ملف Excel:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 10),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: SingleChildScrollView(
                      child: SelectableText(
                        templateData,
                        style: const TextStyle(fontFamily: 'monospace'),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                const Text(
                  'تعليمات:\n'
                  '1. انسخ البيانات أعلاه\n'
                  '2. افتح Excel أو Google Sheets\n'
                  '3. الصق البيانات\n'
                  '4. احفظ الملف بصيغة .xlsx\n'
                  '5. ارفع الملف في التطبيق',
                  style: TextStyle(fontSize: 12),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إغلاق'),
            ),
          ],
        );
      },
    );
  }

  void _clearData() {
    setState(() {
      _studentsToImport.clear();
      _errorMessage = null;
      _successMessage = null;
    });
  }
}
