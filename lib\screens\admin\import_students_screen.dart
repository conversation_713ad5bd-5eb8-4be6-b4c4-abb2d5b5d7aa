import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/student_provider.dart';
import '../../models/student_model.dart';
import '../../utils/app_theme.dart';

class ImportStudentsScreen extends StatefulWidget {
  const ImportStudentsScreen({super.key});

  @override
  State<ImportStudentsScreen> createState() => _ImportStudentsScreenState();
}

class _ImportStudentsScreenState extends State<ImportStudentsScreen> {
  List<Student> _studentsToImport = [];
  bool _isLoading = false;
  String? _errorMessage;
  String? _successMessage;

  // Sample data for demonstration
  final List<Map<String, String>> _sampleData = [
    {
      'full_name': 'أحمد محمد علي',
      'nationality': 'سعودي',
      'phone_number': '**********',
      'class': 'الصف الأول',
      'parent_phone_number': '**********',
    },
    {
      'full_name': 'فاطمة أحمد محمد',
      'nationality': 'مصري',
      'phone_number': '**********',
      'class': 'الصف الثاني',
      'parent_phone_number': '**********',
    },
    {
      'full_name': 'محمد علي أحمد',
      'nationality': 'أردني',
      'phone_number': '0503333333',
      'class': 'الصف الثالث',
      'parent_phone_number': '0509999999',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('استيراد الطلاب (تجريبي)'),
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Instructions Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info, color: AppTheme.infoColor),
                        const SizedBox(width: 8),
                        const Text(
                          'نسخة تجريبية - استيراد الطلاب',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      'هذه نسخة تجريبية من ميزة استيراد الطلاب.\n'
                      'يمكنك:\n'
                      '• تحميل بيانات تجريبية للاختبار\n'
                      '• مراجعة البيانات قبل الحفظ\n'
                      '• حفظ الطلاب في النظام\n\n'
                      'في النسخة الكاملة ستتمكن من رفع ملفات Excel.',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Load Sample Data Button
            ElevatedButton.icon(
              onPressed: _isLoading ? null : _loadSampleData,
              icon: const Icon(Icons.download),
              label: const Text('تحميل بيانات تجريبية'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryGreen,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),

            const SizedBox(height: 20),

            // Error Message
            if (_errorMessage != null)
              Card(
                color: AppTheme.errorColor.withValues(alpha: 0.1),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(Icons.error, color: AppTheme.errorColor),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: TextStyle(color: AppTheme.errorColor),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            // Success Message
            if (_successMessage != null)
              Card(
                color: AppTheme.successColor.withValues(alpha: 0.1),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(Icons.check_circle, color: AppTheme.successColor),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _successMessage!,
                          style: TextStyle(color: AppTheme.successColor),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            // Loading Indicator
            if (_isLoading)
              const Card(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Row(
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(width: 16),
                      Text('جاري معالجة البيانات...'),
                    ],
                  ),
                ),
              ),

            // Students Preview
            if (_studentsToImport.isNotEmpty) ...[
              const SizedBox(height: 20),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.preview, color: AppTheme.infoColor),
                          const SizedBox(width: 8),
                          Text(
                            'معاينة البيانات (${_studentsToImport.length} طالب)',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        height: 300,
                        child: ListView.builder(
                          itemCount: _studentsToImport.length,
                          itemBuilder: (context, index) {
                            final student = _studentsToImport[index];
                            return Card(
                              margin: const EdgeInsets.only(bottom: 8),
                              child: ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: AppTheme.primaryGreen,
                                  child: Text('${index + 1}'),
                                ),
                                title: Text(student.fullName),
                                subtitle: Text(
                                  'الجنسية: ${student.nationality}\n'
                                  'الهاتف: ${student.phoneNumber}\n'
                                  'الصف: ${student.studentClass}',
                                ),
                                isThreeLine: true,
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _importStudents,
                              icon: const Icon(Icons.save),
                              label: const Text('حفظ جميع الطلاب'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppTheme.successColor,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          ElevatedButton.icon(
                            onPressed: _clearData,
                            icon: const Icon(Icons.clear),
                            label: const Text('مسح'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppTheme.errorColor,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _loadSampleData() {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = null;
    });

    // Simulate loading delay
    Future.delayed(const Duration(seconds: 1), () {
      final students = _sampleData
          .map(
            (data) => Student(
              fullName: data['full_name']!,
              nationality: data['nationality']!,
              phoneNumber: data['phone_number']!,
              studentClass: data['class']!,
              parentPhoneNumber: data['parent_phone_number']!,
              password: 'student123',
            ),
          )
          .toList();

      setState(() {
        _studentsToImport = students;
        _successMessage =
            'تم تحميل ${students.length} طالب من البيانات التجريبية';
        _isLoading = false;
      });
    });
  }

  Future<void> _importStudents() async {
    if (_studentsToImport.isEmpty) return;

    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
        _successMessage = null;
      });

      final studentProvider = Provider.of<StudentProvider>(
        context,
        listen: false,
      );

      final result = await studentProvider.bulkImportStudents(
        _studentsToImport,
      );

      setState(() {
        if (result['success'] == true) {
          _successMessage =
              'تم استيراد ${result['imported_count']} طالب بنجاح!';
          if (result['error_count'] > 0) {
            _successMessage =
                '$_successMessage\nفشل في استيراد ${result['error_count']} طالب.';
          }
          _studentsToImport.clear();
        } else {
          _errorMessage = 'فشل في استيراد الطلاب: ${result['message']}';
        }
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في استيراد الطلاب: $e';
        _isLoading = false;
      });
    }
  }

  void _clearData() {
    setState(() {
      _studentsToImport.clear();
      _errorMessage = null;
      _successMessage = null;
    });
  }
}
